import React, { useState, useEffect } from 'react';
import { YTHDialog, YTHForm } from 'yth-ui';
import taskApi from '@/service/taskApi';
import {
  message,
  Button,
  Spin,
  Input,
  Tabs,
  Table,
  Space,
  Modal,
  Form,
  Select,
  DatePicker,
  Col,
  Row,
} from 'antd';
import { Token } from '@/Constant';
import baseApi from '@/service/baseApi';
import dicParams from '@/pages/InspectionPlan/dicParams';
import { PlusOutlined, DeleteOutlined, EditOutlined } from '@ant-design/icons';

import { BaseResponse } from '@/types/common';
import {
  TaskVo,
  TaskInsertParam,
  TaskUpdateParam,
  TaskDeviceVo,
  TaskPointsVo,
  TaskStartParam,
} from '@/types/task';
import { IYTHColumnProps } from 'yth-ui/es/components/list';
import moment from 'moment';
import style from './task.module.less';

const { TabPane } = Tabs;

/**
 * @description 弹窗参数类型定义
 */
type PropsTypes = {
  /** 弹窗的类别 add 新增 view 查看 edit 编辑 */
  type: string;
  /** 弹窗传入的数据 */
  dataObj: { id?: string; [key: string]: React.Key };
  /** 关闭弹窗的回调函数 */
  closeModal: () => void;
  /** 弹窗是否可见 */
  visible: boolean;
};

/**
 * @description 查看 或新增 modal
 * @param PropsTypes PropsTypes
 */
const AddDialog: React.FC<PropsTypes> = ({ type, dataObj, closeModal = () => {}, visible }) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isCasRequested, setIsCasRequested] = useState<boolean>(false);
  const [taskDevicesList, setTaskDevicesList] = useState<TaskDeviceVo[]>([]);
  // 全部设备列表
  const [devicesList, setDevicesList] = useState<Array<{ label: string; value: string }>>([]);
  const [taskPointsList, setTaskPointsList] = useState<TaskPointsVo[]>([]);
  const [pointModalVisible, setPointModalVisible] = useState<boolean>(false);
  const [currentPoint, setCurrentPoint] = useState<TaskPointsVo>({});
  const [editingPointIndex, setEditingPointIndex] = useState<number>(-1);
  const [currentInspectionMethod, setCurrentInspectionMethod] = useState<string>('');
  const [activeTabKey, setActiveTabKey] = useState<string>('1');
  const [taskTypeOptions, setTaskTypeOptions] = useState<Array<{ label: string; value: string }>>(
    [],
  );
  const [inspectionMethodOptions, setInspectionMethodOptions] = useState<
    Array<{ label: string; value: string }>
  >([]);
  const [taskStatusOptions, setTaskStatusOptions] = useState<
    Array<{ label: string; value: string }>
  >([]);

  // 获取任务状态的实际值（处理字符串或对象数组格式）
  const getTaskStatusValue: (
    taskStatus:
      | string
      | { code?: string; text?: string; id?: string; value?: string; lable?: string }[]
      | undefined,
  ) => string = (taskStatus) => {
    if (typeof taskStatus === 'string') {
      return taskStatus;
    }
    if (Array.isArray(taskStatus) && taskStatus.length > 0) {
      return taskStatus[0].code || taskStatus[0].value || '';
    }
    return '';
  };

  // 表单
  const [form] = Form.useForm();
  const [pointForm] = Form.useForm();

  const { TextArea } = Input;

  // 查询详情
  const queryDataDetail: () => Promise<void> = async () => {
    setIsLoading(true);
    try {
      const res: BaseResponse<TaskVo> = await taskApi.getDetailById({
        id: dataObj.id,
      });
      if (res && res.code && res.code === 200) {
        const formD: TaskVo = res.data;

        // 处理 时间 组件的数据格式
        const processedFormD: TaskVo = { ...formD };
        if (formD.planEndTime) {
          processedFormD.planEndTime = moment(formD.planEndTime);
        }
        if (formD.planStartTime) {
          processedFormD.planStartTime = moment(formD.planStartTime);
        }
        if (formD.actualEndTime) {
          processedFormD.actualEndTime = moment(formD.actualEndTime);
        }
        if (formD.actualStartTime) {
          processedFormD.actualStartTime = moment(formD.actualStartTime);
        }
        if(formD.taskDevicesIds){
          processedFormD.taskDevicesIds = formD.taskDevicesIds.split(',');
        }
        form.setFieldsValue(processedFormD);
        setTaskDevicesList(Array.isArray(formD.taskDevicesList) ? formD.taskDevicesList : []);
        setTaskPointsList(Array.isArray(formD.taskPointsList) ? formD.taskPointsList : []);

        // 设置当前巡检方式
        if (typeof formD.inspectionMethod === 'string' && formD.inspectionMethod) {
          setCurrentInspectionMethod(formD.inspectionMethod);
        }
      } else {
        message.error('获取任务详情失败');
        // 即使获取失败，也要确保列表为空数组
        setTaskDevicesList([]);
        setTaskPointsList([]);
      }
    } catch {
      message.error('获取任务详情失败');
      // 确保列表为空数组
      setTaskDevicesList([]);
      setTaskPointsList([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (
      type &&
      (type === 'edit' || type === 'view' || type === 'startTask' || type === 'endTask')
    ) {
      queryDataDetail()
        .then(() => {
          setIsCasRequested(true);
        })
        .catch(() => {
          // 即使查询失败也要显示表单
          setIsCasRequested(true);
        });
    } else {
      // 新增模式下确保列表为空数组
      setTaskDevicesList([]);
      setTaskPointsList([]);
      setIsCasRequested(true);
    }
  }, [type, dataObj]);

  // 加载字典数据 、设备列表
  useEffect(() => {
    const loadDictionaries: () => Promise<void> = async () => {
      try {
        const [taskTypes, inspectionMethods, taskStatuses, devices] = await Promise.all([
          baseApi.getDictionary(dicParams.PLAN_TYPE),
          baseApi.getDictionary(dicParams.INSPECTION_METHOD),
          baseApi.getDictionary(dicParams.TASK_STATUS),
          baseApi.getEquipmentList(dicParams.EQUIPMENT_CATEGORY),
        ]);

        setTaskTypeOptions(taskTypes.map((item) => ({ label: item.text, value: item.code })));
        setInspectionMethodOptions(
          inspectionMethods.map((item) => ({ label: item.text, value: item.code })),
        );
        setTaskStatusOptions(taskStatuses.map((item) => ({ label: item.text, value: item.code })));
        setDevicesList(devices.map((item) => ({ label: item.name, value: item.uniqueCode })));
      } catch (error) {
        console.error('加载字典数据失败:', error);
      }
    };

    loadDictionaries();
  }, []);
  // 处理巡检方式变化
  const handleInspectionMethodChange: (value: unknown) => void = (value: unknown): void => {
    let methodCode: string = '';
    if (Array.isArray(value)) {
      methodCode = value[0]?.code || '';
    } else if (typeof value === 'string') {
      methodCode = value;
    }
    console.log('methodCode', methodCode);
    setCurrentInspectionMethod(methodCode);
    form.setFieldsValue({ inspectionMethod: methodCode });
  };

  // 设置弹窗标题
  const setModalTitle: () => string = (): string => {
    let title: string = '';
    if (type === 'add') {
      title = '新增';
    } else if (type === 'view') {
      title = '查看';
    } else if (type === 'edit') {
      title = '编辑';
    } else if (type === 'startTask') {
      title = '开始任务';
    }
    return title;
  };

  // 处理提交数据的公共方法
  const processSubmitData: (
    data: TaskInsertParam | TaskUpdateParam,
  ) => TaskInsertParam | TaskUpdateParam = (
    data: TaskInsertParam | TaskUpdateParam,
  ): TaskInsertParam | TaskUpdateParam => {
    const submitData: TaskInsertParam | TaskUpdateParam = {
      ...data,
      taskDevicesList,
      taskPointsList,
    };

    // 处理巡检方式字段
    if (data.inspectionMethod) {
      if (Array.isArray(data.inspectionMethod)) {
        submitData.inspectionMethod = data.inspectionMethod[0]?.code || '';
      } else {
        submitData.inspectionMethod = data.inspectionMethod;
      }
    } else {
      submitData.inspectionMethod = '';
    }

    // 处理任务类型字段
    if (data.taskType) {
      if (Array.isArray(data.taskType)) {
        submitData.taskType = data.taskType[0]?.code || '';
      } else {
        submitData.taskType = data.taskType;
      }
    } else {
      submitData.taskType = '';
    }

    // 处理任务状态字段
    if (data.taskStatus) {
      if (Array.isArray(data.taskStatus)) {
        submitData.taskStatus = data.taskStatus[0]?.code || '';
      } else {
        submitData.taskStatus = data.taskStatus;
      }
    } else {
      submitData.taskStatus = '';
    }

    // 处理是否提醒字段
    if (typeof data.isRemind !== 'undefined') {
      if (Array.isArray(data.isRemind)) {
        submitData.isRemind = data.isRemind[0]?.code || null;
      } else {
        submitData.isRemind = data.isRemind;
      }
    }

    // 处理 时间 组件的数据格式
    if (data.planEndTime) {
      submitData.planEndTime = moment(data.planEndTime).format('YYYY-MM-DD HH:mm:ss');
    }
    if (data.planStartTime) {
      submitData.planStartTime = moment(data.planStartTime).format('YYYY-MM-DD HH:mm:ss');
    }
    if (data.actualEndTime) {
      submitData.actualEndTime = moment(data.actualEndTime).format('YYYY-MM-DD HH:mm:ss');
    }
    if (data.actualStartTime) {
      submitData.actualStartTime = moment(data.actualStartTime).format('YYYY-MM-DD HH:mm:ss');
    }

    return submitData;
  };

  // 点击取消
  const cancel: () => void = () => {
    form.resetFields();
    closeModal();
  };

  // 新增保存
  const submitAddData: (data: TaskInsertParam) => Promise<void> = async (data) => {
    setIsLoading(true);
    const submitData: TaskInsertParam = processSubmitData(data) as TaskInsertParam;
    const res: BaseResponse<object> = await taskApi.insert(submitData);
    if (res && res.code && res.code === 200) {
      message.success('新增数据成功');
      closeModal();
    } else {
      message.error('新增数据失败');
    }
    setIsLoading(false);
  };

  // 编辑保存
  const submitEditData: (data: TaskUpdateParam) => Promise<void> = async (data) => {
    setIsLoading(true);
    const processedData: TaskUpdateParam = processSubmitData(data) as TaskUpdateParam;
    const submitData: TaskUpdateParam = {
      ...processedData,
      id: dataObj?.id,
    };
    const res: BaseResponse<object> = await taskApi.update(submitData);
    if (res && res.code && res.code === 200) {
      message.success('更新数据成功');
      closeModal();
    } else {
      message.error('更新数据失败');
    }
    setIsLoading(false);
  };

  // 点击保存
  const save: () => Promise<void> = async () => {
    try {
      const values: TaskInsertParam | TaskUpdateParam = await form.validateFields();
      const submitData: TaskInsertParam = JSON.parse(JSON.stringify(values));
      if (type === 'add') {
        submitAddData(submitData);
        return;
      }
      submitEditData(submitData);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 开始任务
  const satrtTask: () => Promise<void> = async () => {
    try {
      const values: TaskUpdateParam = await form.validateFields();
      const submitData: TaskStartParam = {
        taskId: values.id,
        taskDevicesIds: values.taskDevicesIds,
      };
      if (dicParams.INSPECTION_METHOD_HELMET === submitData.inspectionMethod) {
        if (!submitData.taskDevicesIds) {
          message.error('请选择巡检设备');
          return;
        }
      }
      setIsLoading(true);
      const res: BaseResponse<object> = await taskApi.startTask(submitData);
      if (res && res.code && res.code === 200) {
        message.success('开始任务成功');
        closeModal();
      } else {
        message.error('开始任务失败');
      }
      setIsLoading(false);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 结束任务
  const confirmFinishTask: (taskData: TaskUpdateParam) => Promise<void> = async (taskData) => {
    setIsLoading(true);
    const res: BaseResponse<object> = await taskApi.finishTask(taskData);
    if (res && res.code && res.code === 200) {
      message.success('结束任务成功');
      closeModal();
    } else {
      message.error('结束任务失败');
    }
    setIsLoading(false);
  };

  // 完成任务
  const finishTask: () => Promise<void> = async () => {
    try {
      const values: TaskInsertParam | TaskUpdateParam = await form.validateFields();
      const submitData: TaskInsertParam = JSON.parse(JSON.stringify(values));
      const processedData: TaskUpdateParam = processSubmitData(submitData) as TaskUpdateParam;
      const pointsList: TaskPointsVo[] = processedData.taskPointsList;
      // 是否已完成检查点检查
      let isAllChecked: boolean = true;
      if (pointsList && pointsList.length > 0) {
        pointsList.forEach((item) => {
          if (typeof item.checkStatus === 'undefined' || item.checkStatus === 0) {
            isAllChecked = false;
          }
        });
      }
      if (!isAllChecked) {
        message.error('请先完成各检查点检查！');
        return;
      }
      YTHDialog.show({
        type: 'confirm',
        content: <p>完成任务后数据不可修改，确认完成此任务？</p>,
        onCancle: () => {},
        onConfirm: () => {
          confirmFinishTask(processedData);
        },
        p_props: {
          cancelText: '取消',
          okText: '确定',
          title: '删除',
        },
        m_props: {
          title: '删除',
        },
      });
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const handleEditPoint: (record: TaskPointsVo, index: number) => void = (
    record: TaskPointsVo,
    index: number,
  ) => {
    setCurrentPoint(record);
    setEditingPointIndex(index);
    pointForm.setFieldsValue(record);
    setPointModalVisible(true);
  };

  const handleDeletePoint: (index: number) => void = (index: number) => {
    const newList: TaskPointsVo[] = [...taskPointsList];
    newList.splice(index, 1);
    setTaskPointsList(newList);
  };

  const handlePointOk: () => void = async () => {
    try {
      const pointData: TaskPointsVo = await pointForm.validateFields();
      if (editingPointIndex >= 0) {
        // 编辑
        const newList: TaskPointsVo[] = [...taskPointsList];
        newList[editingPointIndex] = pointData;
        setTaskPointsList(newList);
      } else {
        // 新增
        setTaskPointsList([...taskPointsList, pointData]);
      }
      setPointModalVisible(false);
    } catch (error) {
      console.error('检查点表单验证失败:', error);
    }
  };

  // 检查点表格列定义
  const pointColumns: IYTHColumnProps[] = [
    {
      title: '检查点名称',
      dataIndex: 'pointName',
      key: 'pointName',
    },
    {
      title: '检查点坐标',
      dataIndex: 'pointLocation',
      key: 'pointLocation',
    },
    {
      title: '巡检内容',
      dataIndex: 'inspectionContent',
      key: 'inspectionContent',
    },
    {
      title: '检查状态',
      dataIndex: 'checkStatus',
      key: 'checkStatus',
      render: (status: number) => (status === 1 ? '已检查' : '未检查'),
    },
    {
      title: '检查结果',
      dataIndex: 'checkResult',
      key: 'checkResult',
      render: (result: number) => (result === 1 ? '异常' : '正常'),
    },
    {
      title: '操作',
      key: 'action',
      render: (_: unknown, record: TaskPointsVo, index: number) => (
        <Space size="middle">
          {type !== 'view' && (
            <>
              <Button
                type="link"
                icon={<EditOutlined />}
                onClick={() => handleEditPoint(record, index)}
              >
                编辑
              </Button>
              <Button
                type="link"
                danger
                icon={<DeleteOutlined />}
                onClick={() => handleDeletePoint(index)}
              >
                删除
              </Button>
            </>
          )}
        </Space>
      ),
      dataIndex: '',
    },
  ];

  return (
    <Modal
      width="80%"
      className={style['add-task-moduel']}
      title={setModalTitle()}
      style={{ top: 30 }}
      visible={visible}
      onCancel={closeModal}
      destroyOnClose
      maskClosable={false}
      footer={[
        dicParams.TASK_STATUS_DOING === getTaskStatusValue(dataObj.taskStatus as string) && (
          <Button onClick={save} className={style['search-btn']} type="primary">
            保存
          </Button>
        ),
        dicParams.TASK_STATUS_WAIT === getTaskStatusValue(dataObj.taskStatus as string) && (
          <Button onClick={satrtTask} className={style['search-btn']} type="primary">
            开始任务
          </Button>
        ),
        dicParams.TASK_STATUS_DOING === getTaskStatusValue(dataObj.taskStatus as string) && (
          <Button onClick={finishTask} className={style['search-btn']} type="primary">
            完成任务
          </Button>
        ),

        <Button key="cancel" onClick={cancel} className={style['reset-btn']}>
          取消
        </Button>,
      ]}
    >
      <Spin spinning={isLoading}>
        <div className={style['yth-inspection-moduel']}>
          {isCasRequested && (
            <div>
              <div className={style['task-form-title']}>巡检任务信息</div>
              <Form
                form={form}
                size="small"
                colon={false}
                labelCol={{ span: 6 }}
                className={style['task-form']}
              >
                <Form.Item name="id" label="id" hidden>
                  <Input disabled />
                </Form.Item>
                <Row>
                  <Col span={12}>
                    <Form.Item name="taskCode" label="任务编码">
                      <Input disabled />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="taskName"
                      label="任务名称"
                      rules={[{ required: true, message: '请输入任务名称' }]}
                    >
                      <Input disabled />
                    </Form.Item>
                  </Col>
                </Row>
                <Row>
                  <Col span={12}>
                    <Form.Item name="taskType" label="任务类型">
                      <Select disabled options={taskTypeOptions} />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item name="planName" label="所属计划名称">
                      <Input disabled />
                    </Form.Item>
                    <Form.Item hidden name="planId" label="所属计划">
                      <Input />
                    </Form.Item>
                  </Col>
                </Row>
                <Row>
                  <Col span={12}>
                    <Form.Item name="inspectionMethod" label="巡检方式">
                      <Select
                        disabled
                        options={inspectionMethodOptions}
                        onChange={handleInspectionMethodChange}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item name="directorUserName" label="负责人">
                      <Input />
                    </Form.Item>
                    {/* <YTHForm.Item
                  name="planPoints"
                  title="巡检路线"
                  labelType={2}
                  required
                  componentName="Input"
                  componentProps={{
                    disabled: type === 'view',
                    placeholder: '请输入负责人',
                  }}
                /> */}
                  </Col>
                </Row>
                <Row>
                  <Col span={24}>
                    <Form.Item name="partyUserIds" labelCol={{ span: 3 }} label="参与巡检人">
                      <Input disabled />
                    </Form.Item>
                  </Col>
                </Row>

                <Row>
                  <Col span={3} className={style['lable-col']}>
                    <Form.Item
                      label="计划开始时间"
                      colon={false}
                      className={style['lable-form-item']}
                      labelCol={{ span: 24 }}
                    />
                  </Col>
                  <Col span={9}>
                    <Row>
                      <Col span={11} className={style['start-time-item']}>
                        <Form.Item name="planStartTime" label="计划开始时间" labelCol={{ span: 0 }}>
                          <DatePicker
                            showTime
                            format="YYYY-MM-DD HH:mm:ss"
                            placeholder="计划开始时间"
                            disabled
                            style={{ width: '100%' }}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={13} className={style['lable-col']}>
                        <Form.Item
                          name="planEndTime"
                          label="至"
                          labelCol={{ span: 5 }}
                          className={style['center-label-form-item']}
                        >
                          <DatePicker
                            showTime
                            format="YYYY-MM-DD HH:mm:ss"
                            placeholder="计划结束时间"
                            disabled
                            style={{ width: '100%' }}
                          />
                        </Form.Item>
                      </Col>
                    </Row>
                  </Col>
                  <Col span={3} className={style['lable-col']}>
                    <Form.Item
                      label="实际开始时间"
                      colon={false}
                      className={style['lable-form-item']}
                      labelCol={{ span: 24 }}
                    />
                  </Col>
                  <Col span={9}>
                    <Row>
                      <Col span={11}>
                        <Form.Item
                          name="actualStartTime"
                          label="实际开始时间"
                          labelCol={{ span: 0 }}
                        >
                          <DatePicker
                            showTime
                            placeholder="实际开始时间"
                            format="YYYY-MM-DD HH:mm:ss"
                            disabled
                            style={{ width: '100%' }}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={13} className={style['lable-col']}>
                        <Form.Item
                          name="actualEndTime"
                          label="至"
                          className={style['center-label-form-item']}
                          labelCol={{ span: 5 }}
                        >
                          <DatePicker
                            showTime
                            format="YYYY-MM-DD HH:mm:ss"
                            disabled
                            placeholder="实际结束时间"
                            style={{ width: '100%' }}
                          />
                        </Form.Item>
                      </Col>
                    </Row>
                  </Col>
                </Row>
                <Row style={{ overflow: 'visible' }}>
                  <Col span={12} className={style['lable-col']}>
                    <Form.Item name="taskDevicesIds" label="巡检设备" labelCol={{ span: 6 }}>
                      <Select
                        mode="multiple"
                        disabled={
                          type === 'view' ||
                          dicParams.TASK_STATUS_FINISH === dataObj.taskStatus ||
                          dicParams.INSPECTION_METHOD_HELMET !== currentInspectionMethod
                        }
                        placeholder="请选择巡检设备"
                        options={devicesList.map((device) => ({
                          label: `${device.label}`,
                          value: device.value,
                        }))}
                        allowClear
                        showSearch
                        filterOption={(input, option) =>
                          (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                        }
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="taskStatus"
                      label="任务状态"
                      labelCol={{ span: 6 }}
                      wrapperCol={{ span: 18 }}
                    >
                      <Select disabled options={taskStatusOptions} />
                    </Form.Item>
                  </Col>
                </Row>
                <Row style={{ height: 'auto' }}>
                  <Col span={24}>
                    <Form.Item name="remark" label="备注" labelCol={{ span: 3 }}>
                      <TextArea
                        disabled={type !== 'startTask'}
                        placeholder="请输入备注信息"
                        rows={3}
                      />
                    </Form.Item>
                  </Col>
                </Row>

                <Row style={{ height: '1px' }}>
                  <Col span={24}>
                    <Form.Item name="isRemind" label="是否发送短信提醒" hidden>
                      <Select
                        disabled={type === 'view'}
                        placeholder="请选择是否发送短信提醒"
                        options={[
                          { label: '不发送', value: 0 },
                          { label: '发送', value: 1 },
                        ]}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              </Form>

              <div style={{ marginTop: 20 }}>
                <Tabs
                  defaultActiveKey="1"
                  activeKey={activeTabKey}
                  onChange={setActiveTabKey}
                  tabBarExtraContent={
                    activeTabKey === '1' &&
                    currentInspectionMethod === dicParams.UAV && (
                      <Button type="primary" icon={<PlusOutlined />}>
                        添加检查点
                      </Button>
                    )
                  }
                >
                  <TabPane tab="任务检查点" key="1">
                    <Table
                      columns={pointColumns}
                      dataSource={taskPointsList}
                      rowKey={(record, index) => index?.toString() || '0'}
                      pagination={false}
                      size="small"
                    />
                  </TabPane>
                </Tabs>
              </div>
            </div>
          )}

          {/* 检查点编辑弹窗 */}
          <Modal
            title={editingPointIndex >= 0 ? '编辑检查点' : '添加检查点'}
            visible={pointModalVisible}
            onOk={handlePointOk}
            onCancel={() => setPointModalVisible(false)}
            destroyOnClose
          >
            <Form form={pointForm} layout="vertical">
              <YTHForm.Item
                name="pointName"
                title="检查点名称"
                labelType={2}
                required
                componentName="Input"
                componentProps={{
                  placeholder: '请输入检查点名称',
                }}
              />
              <YTHForm.Item
                name="pointLocation"
                title="检查点坐标"
                labelType={2}
                required
                componentName="Input"
                componentProps={{
                  placeholder: '请输入检查点坐标',
                }}
              />
              <YTHForm.Item
                name="inspectionContent"
                title="巡检内容"
                labelType={2}
                required
                component={TextArea}
                componentProps={{
                  placeholder: '请输入巡检内容',
                  rows: 3,
                }}
              />
              <YTHForm.Item
                name="checkStatus"
                title="检查状态"
                labelType={2}
                componentName="Selector"
                componentProps={{
                  request: async () => {
                    return [
                      { code: '0', text: '未检查' },
                      { code: '1', text: '已检查' },
                    ];
                  },
                  p_props: {
                    placeholder: '请选择检查状态',
                  },
                }}
              />
              <YTHForm.Item
                name="checkResult"
                title="检查结果"
                labelType={2}
                componentName="Selector"
                componentProps={{
                  request: async () => {
                    return [
                      { code: '0', text: '正常' },
                      { code: '1', text: '异常' },
                    ];
                  },
                  p_props: {
                    placeholder: '请选择检查结果',
                  },
                }}
              />
              <YTHForm.Item
                name="checkResultDescription"
                title="结果描述"
                labelType={2}
                component={TextArea}
                componentProps={{
                  placeholder: '请输入结果描述',
                  rows: 3,
                }}
              />
              <YTHForm.Item
                name="attachments"
                title="结果照片"
                labelType={2}
                componentName="Upload"
                componentProps={{
                  listType: `yth-card`,
                  name: 'file',
                  action: '/gw/form-api/file/upload',
                  headers: {
                    authorization: Token(),
                  },
                  online: '/preview/onlinePreview',
                  data: {
                    formCode: 'task_attachments',
                  },
                }}
              />
            </Form>
          </Modal>
        </div>
      </Spin>
    </Modal>
  );
};

export default AddDialog;
