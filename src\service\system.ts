import { YTHToast } from 'yth-ui';
import requests from '@/request';

export interface Unit {
  id?: string;
  parentId?: string;
  unitCode?: string;
  unitName?: string;
  shortName?: string;
  path?: string;
  unitType?: string;
  unitManager?: string;
  unitLeader?: string;
  description?: number;
  isGroup?: number;
  sortNo?: string;
  isEnable?: string;
  createBy?: string;
  createDate?: string;
  updateBy?: string;
  updateDate?: string;
  unitId?: string;
  orgId?: string;
  children?: Unit[];
}

export interface User {
  id?: string;
  userName?: string;
  userCode?: string;
  password?: string;
  realName?: string;
  name?: string;
  phone?: string;
  avatar?: string;
  autograph?: string;
  sex?: string;
  email?: string;
  education?: string;
  hireDate?: string;
  birthday?: string;
  state?: string;
  identity?: string;
  type?: string;
  isLock?: string;
  isSystem?: string;
  isEnable?: string;
  sortNo?: number;
  description?: string;
  createBy?: string;
  createDate?: string;
  updateBy?: string;
  updateDate?: string;
  unitId?: string;
  orgId?: string;
  isDelete?: string;
  view?: boolean;
}

export interface Post {
  id?: string;
  postCode?: string;
  postName?: string;
  postType?: string;
  isEnable?: string;
  isSystem?: string;
  postDesc?: string;
  createBy?: string;
  createDate?: string;
  updateBy?: string;
  updateDate?: string;
  unitId?: string;
  orgId?: string;
}

export interface Role {
  id?: string;
  roleCode?: string;
  roleName?: string;
  roleType?: string;
  isEnable?: string;
  isSystem?: string;
  isPublic?: string;
  roleDesc?: string;
  createBy?: string;
  createDate?: string;
  updateBy?: string;
  updateDate?: string;
  unitId?: string;
  orgId?: string;
}

/**
 * 公共设备信息
 */
export interface Equipment {
  id?: string;
  /** 设备分类 */
  category?: string;
  /** 设备名称 */
  name?: string;
  /** 企业ID */
  orgId?: string;
  /** 设备状态 */
  status?: string;
  /** 设备类型 */
  type?: string;
  /** 设备唯一码 */
  uniqueCode?: string;
}
export default {
  /**
   * @param url
   * @returns Promise<Unit | null>
   * @description 获取组织机构树形结构数据
   */
  unitTree: async (url = 'sys/unit/unitTree') => {
    const resData: {
      data: Unit;
      code: number;
      msg: string;
    } = await requests<{
      data: Unit;
      code: number;
      msg: string;
    }>(url, {
      method: 'GET',
    });
    if (resData.code === 200) {
      return resData.data;
    }
    YTHToast.show({
      type: 'error',
      messageText: `服务器错误，${resData.msg}`,
      p_props: { duration: 10 },
      m_props: { duration: 3000 },
    });
    return null;
  },
  /**
   * @param url
   * @returns Promise<Unit | null>
   * @description 获取组织机构简单树形结构数据
   */
  simpleCompanyTree: async (url = 'sys/unit/simpleCompanyTree') => {
    const resData: {
      data: Unit;
      code: number;
      msg: string;
    } = await requests<{
      data: Unit;
      code: number;
      msg: string;
    }>(url, {
      method: 'GET',
    });
    if (resData.code === 200) {
      return resData.data;
    }
    YTHToast.show({
      type: 'error',
      messageText: `服务器错误，${resData.msg}`,
      p_props: { duration: 10 },
      m_props: { duration: 3000 },
    });
    return null;
  },
  /**
   * @param url
   * @param unitId 企业id
   * @returns Promise<User | null>
   * @description 获取用户列表
   */
  getUserList: async (url = '/sys/user/userList', unitId = '') => {
    const resData: {
      data: User;
      code: number;
      msg: string;
    } = await requests<{
      data: User;
      code: number;
      msg: string;
    }>(url, {
      method: 'GET',
      params: { unitId },
    });
    if (resData.code === 200) {
      return resData.data;
    }
    YTHToast.show({
      type: 'error',
      messageText: `服务器错误，${resData.msg}`,
      p_props: { duration: 10 },
      m_props: { duration: 3000 },
    });
    return null;
  },
  /**
   * @param url
   * @returns Promise<Role | null>
   * @description 获取角色列表
   */
  getUserRoles: async (url = '/sys/role/userRoles') => {
    const resData: {
      data: Role;
      code: number;
      msg: string;
    } = await requests<{
      data: Role;
      code: number;
      msg: string;
    }>(url, {
      method: 'GET',
    });
    if (resData.code === 200) {
      return resData.data;
    }
    YTHToast.show({
      type: 'error',
      messageText: `服务器错误，${resData.msg}`,
      p_props: { duration: 10 },
      m_props: { duration: 3000 },
    });
    return null;
  },
  /**
   * @param url
   * @returns Promise<Post | null>
   * @description 获取岗位列表
   */
  getPostList: async (url = '/sys/post/list') => {
    const resData: {
      data: Post;
      code: number;
      msg: string;
    } = await requests<{
      data: Post;
      code: number;
      msg: string;
    }>(url, {
      method: 'GET',
    });
    if (resData.code === 200) {
      return resData.data;
    }
    YTHToast.show({
      type: 'error',
      messageText: `服务器错误，${resData.msg}`,
      p_props: { duration: 10 },
      m_props: { duration: 3000 },
    });
    return null;
  },
};
